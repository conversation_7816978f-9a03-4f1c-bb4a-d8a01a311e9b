
# Makefile for metrics and build

.PHONY: help metrics gov_stage_apk public_stage_apk gov_release public_release all

help:
	@echo "Available targets:"
	@echo "  metrics            - Run metrics collection script."
	@echo "  gov_stage_apk      - Build private app (gov) for Android, stage env, fat APK."
	@echo "  public_stage_apk   - Build public app for Android, stage env, fat APK."
	@echo "  gov_release        - Build private app (gov) for Android, prod env, split APK."
	@echo "  public_release     - Build public app for Android, stage env, fat APK."
	@echo "  help               - Show this help message."

.PHONY: metrics build all

metrics:
	./metrics/scripts/collect_metrics.sh

gov_stage_apk:
	fastlane build app:private env:stage platform:android artifacts:fat_apk

public_stage_apk:
	fastlane build app:public env:stage platform:android artifacts:fat_apk

gov_release_apk:
	fastlane build app:private env:prod platform:android artifacts:split_apk

public_release_apk:
	fastlane build app:public env:stage platform:android artifacts:fat_apk
