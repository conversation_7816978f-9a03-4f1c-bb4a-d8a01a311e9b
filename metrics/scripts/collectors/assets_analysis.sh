#!/bin/bash

# Assets Analysis Collector Script
# Analyzes asset files and generates top 10 largest files for private and public apps

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color


# Parse format argument
FORMAT="text"
if [ $# -ge 1 ]; then
    if [ "$1" = "json" ] || [ "$1" = "text" ]; then
        FORMAT="$1"
    fi
fi

# Get the project root (assuming script is run from project root)
PROJECT_ROOT="$(pwd)"

# Function to format file size in human readable format
format_size() {
    local size_bytes="$1"
    
    if [ "$size_bytes" -eq 0 ]; then
        echo "0 B"
        return
    fi
    
    local units=("B" "KB" "MB" "GB")
    local unit_index=0
    local size=$size_bytes
    
    while [ "$size" -ge 1024 ] && [ "$unit_index" -lt 3 ]; do
        size=$((size / 1024))
        unit_index=$((unit_index + 1))
    done
    
    echo "${size} ${units[$unit_index]}"
}


# Function to get top 10 largest files in a directory (returns arrays for JSON)
get_top_assets_json() {
    local assets_dir="$1"
    local temp_file=$(mktemp)
    find "$assets_dir" -type f -exec ls -la {} \; 2>/dev/null | \
    awk '{
        size = $5
        filename = ""
        for(i=9; i<=NF; i++) {
            if(i==9) filename = $i
            else filename = filename " " $i
        }
        print size "\t" filename
    }' | \
    sort -nr | \
    head -10 > "$temp_file"
    local file_count=$(wc -l < "$temp_file")
    local rank=1
    local total_size=0
    local json_list=""
    while IFS=$'\t' read -r size filename; do
        if [ ! -z "$size" ] && [ ! -z "$filename" ]; then
            local clean_filename=$(basename "$filename")
            json_list="$json_list{\"rank\":$rank,\"name\":\"$clean_filename\",\"size\":$size}"
            rank=$((rank + 1))
            total_size=$((total_size + size))
            [ $rank -le $file_count ] && json_list="$json_list,"
        fi
    done < "$temp_file"
    rm "$temp_file"
    echo "$json_list"
}

get_top_assets_text() {
    local assets_dir="$1"
    local app_name="$2"
    echo -e "${CYAN}📁 Analyzing ${app_name} assets...${NC}"
    if [ ! -d "$assets_dir" ]; then
        echo -e "${RED}❌ Assets directory not found: ${assets_dir}${NC}"
        return
    fi
    local temp_file=$(mktemp)
    find "$assets_dir" -type f -exec ls -la {} \; 2>/dev/null | \
    awk '{
        size = $5
        filename = ""
        for(i=9; i<=NF; i++) {
            if(i==9) filename = $i
            else filename = filename " " $i
        }
        print size "\t" filename
    }' | \
    sort -nr | \
    head -10 > "$temp_file"
    local file_count=$(wc -l < "$temp_file")
    if [ "$file_count" -eq 0 ]; then
        echo -e "${YELLOW}📝 No asset files found in ${app_name}${NC}"
        rm "$temp_file"
        return
    fi
    echo -e "${GREEN}📊 Top 10 Largest Assets in ${app_name}:${NC}"
    echo "=================================="
    local rank=1
    local total_size=0
    while IFS=$'\t' read -r size filename; do
        if [ ! -z "$size" ] && [ ! -z "$filename" ]; then
            local clean_filename=$(basename "$filename")
            local formatted_size=$(format_size "$size")
            printf "%2d. %-30s %10s\n" "$rank" "$clean_filename" "$formatted_size"
            total_size=$((total_size + size))
            rank=$((rank + 1))
        fi
    done < "$temp_file"
    echo "=================================="
    local formatted_total=$(format_size "$total_size")
    echo -e "Total size of top 10: ${GREEN}${formatted_total}${NC}"
    local all_files_count=$(find "$assets_dir" -type f 2>/dev/null | wc -l | tr -d ' ')
    local all_files_size=$(find "$assets_dir" -type f -exec ls -la {} \; 2>/dev/null | awk '{sum += $5} END {print sum+0}')
    local formatted_all_size=$(format_size "$all_files_size")
    echo -e "Total assets: ${YELLOW}${all_files_count} files${NC}, ${YELLOW}${formatted_all_size}${NC}"
    echo ""
    rm "$temp_file"
}

analyze_asset_types() {
    local assets_dir="$1"
    local app_name="$2"
    
    if [ ! -d "$assets_dir" ]; then
        return
    fi
    
    echo -e "${CYAN}${app_name}:${NC}"
    
    # Common asset extensions
    local extensions=("png" "jpg" "jpeg" "gif" "svg" "webp" "mp3" "wav" "mp4" "mov" "pdf" "json" "txt" "xml")
    
    for ext in "${extensions[@]}"; do
        local count=$(find "$assets_dir" -name "*.${ext}" -type f 2>/dev/null | wc -l | tr -d ' ')
        local size=$(find "$assets_dir" -name "*.${ext}" -type f -exec ls -la {} \; 2>/dev/null | awk '{sum += $5} END {print sum+0}')
        
        if [ "$count" -gt 0 ]; then
            local formatted_size=$(format_size "$size")
            printf "  %-8s: %3s files, %10s\n" ".${ext}" "$count" "$formatted_size"
        fi
    done
    
    # Other files (without extension or unknown extensions)
    local other_files=$(find "$assets_dir" -type f 2>/dev/null | grep -v -E '\.(png|jpg|jpeg|gif|svg|webp|mp3|wav|mp4|mov|pdf|json|txt|xml)$')
    local other_count=$(echo "$other_files" | grep -v "^$" | wc -l | tr -d ' ')
    if [ "$other_count" -gt 0 ]; then
        local other_size=0
        while IFS= read -r file; do
            if [ -f "$file" ]; then
                local file_size=$(ls -la "$file" 2>/dev/null | awk '{print $5}')
                other_size=$((other_size + file_size))
            fi
        done <<< "$other_files"
        local formatted_other_size=$(format_size "$other_size")
        printf "  %-8s: %3s files, %10s\n" "other" "$other_count" "$formatted_other_size"
    fi
    
    echo ""
}

analyze_asset_types "$PRIVATE_ASSETS_DIR" "Private App"
analyze_asset_types "$PUBLIC_ASSETS_DIR" "Public App"

# JSON version of asset type breakdown
analyze_asset_types_json() {
    local assets_dir="$1"
    local extensions=("png" "jpg" "jpeg" "gif" "svg" "webp" "mp3" "wav" "mp4" "mov" "pdf" "json" "txt" "xml")
    local json_list=""
    for ext in "${extensions[@]}"; do
        local count=$(find "$assets_dir" -name "*.${ext}" -type f 2>/dev/null | wc -l | tr -d ' ')
        local size=$(find "$assets_dir" -name "*.${ext}" -type f -exec ls -la {} \; 2>/dev/null | awk '{sum += $5} END {print sum+0}')
        if [ "$count" -gt 0 ]; then
            json_list="$json_list{\"ext\":\"$ext\",\"count\":$count,\"size\":$size},"
        fi
    done
    # Other files
    local other_files=$(find "$assets_dir" -type f 2>/dev/null | grep -v -E '\.(png|jpg|jpeg|gif|svg|webp|mp3|wav|mp4|mov|pdf|json|txt|xml)$')
    local other_count=$(echo "$other_files" | grep -v "^$" | wc -l | tr -d ' ')
    if [ "$other_count" -gt 0 ]; then
        local other_size=0
        while IFS= read -r file; do
            if [ -f "$file" ]; then
                local file_size=$(ls -la "$file" 2>/dev/null | awk '{print $5}')
                other_size=$((other_size + file_size))
            fi
        done <<< "$other_files"
        json_list="$json_list{\"ext\":\"other\",\"count\":$other_count,\"size\":$other_size},"
    fi
    # Remove trailing comma
    json_list=$(echo "$json_list" | sed 's/,$//')
    echo "$json_list"
}

PRIVATE_ASSETS_DIR="$PROJECT_ROOT/apps/private/assets"
PUBLIC_ASSETS_DIR="$PROJECT_ROOT/apps/public/assets"

if [ "$FORMAT" = "json" ]; then
  printf '{\n'
  printf '  "private": {\n'
  printf '    "top_assets": ['
  get_top_assets_json "$PRIVATE_ASSETS_DIR"
  printf '],\n'
  printf '    "types": ['
  analyze_asset_types_json "$PRIVATE_ASSETS_DIR"
  printf ']\n'
  printf '  },\n'
  printf '  "public": {\n'
  printf '    "top_assets": ['
  get_top_assets_json "$PUBLIC_ASSETS_DIR"
  printf '],\n'
  printf '    "types": ['
  analyze_asset_types_json "$PUBLIC_ASSETS_DIR"
  printf ']\n'
  printf '  }\n'
  printf '}\n'
else
  echo -e "${BLUE}🎨 Assets Analysis${NC}"
  echo "=================================="
  echo -e "${YELLOW}Starting assets analysis...${NC}"
  echo ""
  get_top_assets_text "$PRIVATE_ASSETS_DIR" "Private App"
  get_top_assets_text "$PUBLIC_ASSETS_DIR" "Public App"
  echo -e "${BLUE}📋 Asset Types Breakdown:${NC}"
  echo "=================================="
  analyze_asset_types "$PRIVATE_ASSETS_DIR" "Private App"
  analyze_asset_types "$PUBLIC_ASSETS_DIR" "Public App"
  echo -e "${GREEN}✅ Assets analysis complete!${NC}"
fi
