#!/bin/bash

# File Count Collector Script
# Counts project files by category (.dart, .java, .kt, .swift files only)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color


# Parse format argument
FORMAT="text"
if [ $# -ge 1 ]; then
    if [ "$1" = "json" ] || [ "$1" = "text" ]; then
        FORMAT="$1"
    fi
fi

# Get the project root (assuming script is run from project root)
PROJECT_ROOT="$(pwd)"

# File extensions to count
EXTENSIONS=("dart" "java" "kt" "swift" "rb" "sh" "yaml")

# Function to count files with specific extensions in a directory
count_files() {
    local dir="$1"
    local count=0
    
    if [ ! -d "$dir" ]; then
        echo 0
        return
    fi
    
    for ext in "${EXTENSIONS[@]}"; do
        local ext_count=$(find "$dir" -name "*.${ext}" -type f 2>/dev/null | wc -l | tr -d ' ')
        count=$((count + ext_count))
    done
    
    echo "$count"
}


# --- Collect all data first (Bash 3.2 compatible) ---
EXT_KEYS=()
EXT_VALUES=()

TOTAL_COUNT=$(find "$PROJECT_ROOT" \
        -type f \
        \( -name "*.dart" -o -name "*.java" -o -name "*.kt" -o -name "*.swift" \) \
        -not -path "*/build/*" \
        -not -path "*/.git/*" \
        -not -path "*/node_modules/*" \
        -not -path "*/.pub-cache/*" \
        -not -path "*/.flutter-plugins*" \
        2>/dev/null | wc -l | tr -d ' ')

PRIVATE_COUNT=$(count_files "$PROJECT_ROOT/apps/private")
PUBLIC_COUNT=$(count_files "$PROJECT_ROOT/apps/public")
PACKAGES_COUNT=$(count_files "$PROJECT_ROOT/packages")
ANDROID_LIBS_COUNT=$(count_files "$PROJECT_ROOT/android-libs")

for ext in "${EXTENSIONS[@]}"; do
        count=$(find "$PROJECT_ROOT" \
                -name "*.${ext}" \
                -not -path "*/build/*" \
                -not -path "*/.git/*" \
                -not -path "*/node_modules/*" \
                -not -path "*/.pub-cache/*" \
                -not -path "*/.flutter-plugins*" \
                -type f 2>/dev/null | wc -l | tr -d ' ')
        EXT_KEYS+=("$ext")
        EXT_VALUES+=("$count")
done

if [ "$FORMAT" = "json" ]; then
    # Output JSON only
    printf '{\n'
    printf '  "total_files": %s,\n' "$TOTAL_COUNT"
    printf '  "private_files": %s,\n' "$PRIVATE_COUNT"
    printf '  "public_files": %s,\n' "$PUBLIC_COUNT"
    printf '  "packages_files": %s,\n' "$PACKAGES_COUNT"
    printf '  "android_libs_files": %s,\n' "$ANDROID_LIBS_COUNT"
    printf '  "by_filetype": {\n'
    for i in "${!EXT_KEYS[@]}"; do
        ext="${EXT_KEYS[$i]}"; val="${EXT_VALUES[$i]}"
        printf '    "%s": %s' "$ext" "$val"
        [ $i -lt $((${#EXT_KEYS[@]}-1)) ] && printf ','
        printf '\n'
    done
    printf '  }\n'
    printf '}\n'
else
    # Human readable output using JSON data
    echo -e "${BLUE}📁 File Count Analysis${NC}"
    echo "=================================="
    echo -e "${YELLOW}Counting files...${NC}"
    echo ""
    echo -e "${GREEN}📁 File Count Results:${NC}"
    echo "=================================="
    echo -e "Total project files:     ${GREEN}${TOTAL_COUNT}${NC}"
    echo -e "Private app files:       ${BLUE}${PRIVATE_COUNT}${NC}"
    echo -e "Public app files:        ${BLUE}${PUBLIC_COUNT}${NC}"
    echo -e "Shared packages files:   ${YELLOW}${PACKAGES_COUNT}${NC}"
    echo -e "Android libs files:      ${YELLOW}${ANDROID_LIBS_COUNT}${NC}"
    echo ""
    echo -e "${GREEN}📋 Breakdown by File Type:${NC}"
    echo "=================================="
    for i in "${!EXT_KEYS[@]}"; do
        printf "%-10s files: %s\n" ".${EXT_KEYS[$i]}" "${EXT_VALUES[$i]}"
    done
    echo ""
    echo -e "${GREEN}✅ File count analysis complete!${NC}"
fi
