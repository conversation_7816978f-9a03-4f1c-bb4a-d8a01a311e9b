#!/bin/bash
# Collects Dart analysis issues grouped by severity from diagnostics key

# Parse format argument
FORMAT="text"
if [ $# -ge 1 ]; then
	if [ "$1" = "json" ] || [ "$1" = "text" ]; then
		FORMAT="$1"
	fi
fi

output=$(dart analyze --format=json)

errors=$(echo "$output" | jq '[.diagnostics[] | select(.severity=="ERROR")] | length')
warnings=$(echo "$output" | jq '[.diagnostics[] | select(.severity=="WARNING")] | length')
infos=$(echo "$output" | jq '[.diagnostics[] | select(.severity=="INFO")] | length')

if [ "$FORMAT" = "json" ]; then
	printf '{\n'
	printf '  "errors": %s,\n' "$errors"
	printf '  "warnings": %s,\n' "$warnings"
	printf '  "info": %s\n' "$infos"
	printf '}\n'
else
	echo "Dart Analysis Issues:"
	echo "Errors: $errors"
	echo "Warnings: $warnings"
	echo "Info: $infos"
fi