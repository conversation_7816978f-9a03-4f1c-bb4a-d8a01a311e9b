#!/bin/bash

# Lines of Code Collector Script
# Counts lines of code by category (.dart, .java, .kt, .swift files only)


set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color


# Parse format argument
FORMAT="text"
if [ $# -ge 1 ]; then
    if [ "$1" = "json" ] || [ "$1" = "text" ]; then
        FORMAT="$1"
    fi
fi

# Get the project root (assuming script is run from project root)
PROJECT_ROOT="$(pwd)"

# File extensions to count
EXTENSIONS=("dart" "java" "kt" "swift" "rb" "sh" "yaml")

# Function to count lines of code with specific extensions in a directory
count_loc() {
    local dir="$1"
    local total_lines=0
    
    if [ ! -d "$dir" ]; then
        echo 0
        return
    fi
    
    for ext in "${EXTENSIONS[@]}"; do
        local ext_lines=$(find "$dir" -name "*.${ext}" -type f -exec wc -l {} + 2>/dev/null | grep -E "total$" | awk '{sum += $1} END {print sum+0}')
        if [ -z "$ext_lines" ]; then
            ext_lines=0
        fi
        total_lines=$((total_lines + ext_lines))
    done
    
    echo "$total_lines"
}

# Function to count lines for specific file extensions across a directory
count_loc_by_extension() {
    local dir="$1"
    local ext="$2"
    
    if [ ! -d "$dir" ]; then
        echo 0
        return
    fi
    
    local lines=$(find "$dir" -name "*.${ext}" -type f -exec wc -l {} + 2>/dev/null | grep -E "total$" | awk '{print $1}')
    if [ -z "$lines" ]; then
        lines=0
    fi
    echo "$lines"
}



# --- Collect all data first (Bash 3.2 compatible) ---
TOTAL_LOC=0
EXT_LOC_KEYS=()
EXT_LOC_VALUES=()
PRIVATE_EXT_KEYS=()
PRIVATE_EXT_VALUES=()
PUBLIC_EXT_KEYS=()
PUBLIC_EXT_VALUES=()

for ext in "${EXTENSIONS[@]}"; do
    ext_lines=$(find "$PROJECT_ROOT" \
        -name "*.${ext}" \
        -not -path "*/build/*" \
        -not -path "*/.git/*" \
        -not -path "*/node_modules/*" \
        -not -path "*/.pub-cache/*" \
        -not -path "*/.flutter-plugins*" \
        -type f -exec wc -l {} + 2>/dev/null | grep -E "total$" | awk '{print $1}')
    if [ ! -z "$ext_lines" ]; then
        TOTAL_LOC=$((TOTAL_LOC + ext_lines))
    else
        ext_lines=0
    fi
    EXT_LOC_KEYS+=("$ext")
    EXT_LOC_VALUES+=("$ext_lines")
done

PRIVATE_LOC=$(count_loc "$PROJECT_ROOT/apps/private")
PUBLIC_LOC=$(count_loc "$PROJECT_ROOT/apps/public")
PACKAGES_LOC=$(count_loc "$PROJECT_ROOT/packages")
ANDROID_LIBS_LOC=$(count_loc "$PROJECT_ROOT/android-libs")

for ext in "${EXTENSIONS[@]}"; do
    PRIVATE_EXT_KEYS+=("$ext")
    PRIVATE_EXT_VALUES+=("$(count_loc_by_extension "$PROJECT_ROOT/apps/private" "$ext")")
    PUBLIC_EXT_KEYS+=("$ext")
    PUBLIC_EXT_VALUES+=("$(count_loc_by_extension "$PROJECT_ROOT/apps/public" "$ext")")
done

if [ "$FORMAT" = "json" ]; then
    # Output JSON only
    printf '{\n'
    printf '  "total_loc": %s,\n' "$TOTAL_LOC"
    printf '  "private_loc": %s,\n' "$PRIVATE_LOC"
    printf '  "public_loc": %s,\n' "$PUBLIC_LOC"
    printf '  "packages_loc": %s,\n' "$PACKAGES_LOC"
    printf '  "android_libs_loc": %s,\n' "$ANDROID_LIBS_LOC"
    printf '  "by_filetype": {\n'
    for i in "${!EXT_LOC_KEYS[@]}"; do
        ext="${EXT_LOC_KEYS[$i]}"; val="${EXT_LOC_VALUES[$i]}"
        printf '    "%s": %s' "$ext" "$val"
        [ $i -lt $((${#EXT_LOC_KEYS[@]}-1)) ] && printf ','
        printf '\n'
    done
    printf '  },\n'
    printf '  "private_app": {\n'
    for i in "${!PRIVATE_EXT_KEYS[@]}"; do
        ext="${PRIVATE_EXT_KEYS[$i]}"; val="${PRIVATE_EXT_VALUES[$i]}"
        printf '    "%s": %s' "$ext" "$val"
        [ $i -lt $((${#PRIVATE_EXT_KEYS[@]}-1)) ] && printf ','
        printf '\n'
    done
    printf '  },\n'
    printf '  "public_app": {\n'
    for i in "${!PUBLIC_EXT_KEYS[@]}"; do
        ext="${PUBLIC_EXT_KEYS[$i]}"; val="${PUBLIC_EXT_VALUES[$i]}"
        printf '    "%s": %s' "$ext" "$val"
        [ $i -lt $((${#PUBLIC_EXT_KEYS[@]}-1)) ] && printf ','
        printf '\n'
    done
    printf '  }\n'
    printf '}\n'
else
    # Human readable output using JSON data
    echo -e "${BLUE}📝 Lines of Code Analysis${NC}"
    echo "=================================="
    echo -e "${YELLOW}Counting lines of code...${NC}"
    echo ""
    echo -e "${GREEN}📝 Lines of Code Results:${NC}"
    echo "=================================="
    echo -e "Total project LOC:       ${GREEN}${TOTAL_LOC}${NC}"
    echo -e "Private app LOC:         ${BLUE}${PRIVATE_LOC}${NC}"
    echo -e "Public app LOC:          ${BLUE}${PUBLIC_LOC}${NC}"
    echo -e "Shared packages LOC:     ${YELLOW}${PACKAGES_LOC}${NC}"
    echo -e "Android libs LOC:        ${YELLOW}${ANDROID_LIBS_LOC}${NC}"
    echo ""
    echo -e "${GREEN}📋 Breakdown by File Type:${NC}"
    echo "=================================="
    for i in "${!EXT_LOC_KEYS[@]}"; do
        printf "%-10s LOC: %s\n" ".${EXT_LOC_KEYS[$i]}" "${EXT_LOC_VALUES[$i]}"
    done
    echo ""
    echo -e "${GREEN}📊 Detailed Breakdown:${NC}"
    echo "=================================="
    echo -e "${BLUE}Private App:${NC}"
    for i in "${!PRIVATE_EXT_KEYS[@]}"; do
        printf "  %-8s: %s LOC\n" ".${PRIVATE_EXT_KEYS[$i]}" "${PRIVATE_EXT_VALUES[$i]}"
    done
    echo -e "${BLUE}Public App:${NC}"
    for i in "${!PUBLIC_EXT_KEYS[@]}"; do
        printf "  %-8s: %s LOC\n" ".${PUBLIC_EXT_KEYS[$i]}" "${PUBLIC_EXT_VALUES[$i]}"
    done
    echo ""
    echo -e "${GREEN}✅ Lines of code analysis complete!${NC}"
fi
