#!/bin/bash

# Build Analysis Collector Script
# Measures build performance and output sizes for Android and iOS builds
# Supports --debug flag to include debug builds

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color


# Parse format argument and --debug
INCLUDE_DEBUG=false
FORMAT="text"
while [[ $# -gt 0 ]]; do
    case $1 in
        --debug)
            INCLUDE_DEBUG=true
            shift
            ;;
        json|text)
            FORMAT="$1"
            shift
            ;;
        *)
            echo "Unknown option: $1"
            echo "Usage: $0 [--debug] [json|text]"
            exit 1
            ;;
    esac
done


if [ "$FORMAT" = "json" ]; then
    # Suppress all pretty output, only JSON at the end
    exec 3>&1 1>/dev/null
fi

echo -e "${BLUE}🏗️  Build Analysis (Performance & Size)${NC}"
echo "=================================================="
if [ "$INCLUDE_DEBUG" = true ]; then
        echo -e "${YELLOW}📋 Including debug builds${NC}"
else
        echo -e "${YELLOW}📋 Release builds only (use --debug to include debug builds)${NC}"
fi
echo ""

# Get the project root (assuming script is run from project root)
PROJECT_ROOT="$(pwd)"
PRIVATE_APP_DIR="$PROJECT_ROOT/apps/private"
PUBLIC_APP_DIR="$PROJECT_ROOT/apps/public"

# Function to format time in human readable format
format_time() {
    local total_seconds="$1"
    local minutes=$((total_seconds / 60))
    local seconds=$((total_seconds % 60))
    
    if [ "$minutes" -gt 0 ]; then
        echo "${minutes}m ${seconds}s"
    else
        echo "${seconds}s"
    fi
}

# Function to format file size in human readable format
format_size() {
    local size_bytes="$1"
    
    # Handle empty or non-numeric input
    if [ -z "$size_bytes" ] || ! [[ "$size_bytes" =~ ^[0-9]+$ ]]; then
        echo "0 B"
        return
    fi
    
    if [ "$size_bytes" -eq 0 ]; then
        echo "0 B"
        return
    fi
    
    # Convert to appropriate unit
    if [ "$size_bytes" -lt 1024 ]; then
        echo "${size_bytes} B"
    elif [ "$size_bytes" -lt 1048576 ]; then
        local size_kb=$((size_bytes / 1024))
        echo "${size_kb} KB"
    elif [ "$size_bytes" -lt 1073741824 ]; then
        local size_mb=$((size_bytes / 1048576))
        local remainder=$(((size_bytes % 1048576) * 10 / 1048576))
        if [ "$remainder" -gt 0 ]; then
            echo "${size_mb}.${remainder} MB"
        else
            echo "${size_mb} MB"
        fi
    else
        local size_gb=$((size_bytes / 1073741824))
        local remainder=$(((size_bytes % 1073741824) * 10 / 1073741824))
        if [ "$remainder" -gt 0 ]; then
            echo "${size_gb}.${remainder} GB"
        else
            echo "${size_gb} GB"
        fi
    fi
}

# Function to get file size in bytes
get_file_size() {
    local file_path="$1"
    if [ -f "$file_path" ]; then
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            stat -f%z "$file_path"
        else
            # Linux
            stat -c%s "$file_path"
        fi
    else
        echo "0"
    fi
}

# Function to build and measure both time and size
build_and_measure() {
    local app_dir="$1"
    local platform="$2"
    local build_type="$3"
    local app_name="$4"
    
    # Send display output to stderr so it shows but doesn't interfere with result capture
    echo -e "${CYAN}🔨 Building ${app_name} - ${platform} (${build_type})...${NC}" >&2
    
    if [ ! -d "$app_dir" ]; then
        echo -e "${RED}❌ App directory not found: ${app_dir}${NC}" >&2
        echo "0:false:0"
        return
    fi
    
    cd "$app_dir"
    
    local start_time=$(date +%s)
    local build_success=false
    local build_output=""
    local output_size=0
    local output_path=""
    
    case "$platform" in
        "android")
            case "$build_type" in
                "debug")
                    echo "   🤖 Building Android APK (debug)..." >&2
                    build_output=$(flutter build apk --debug 2>&1)
                    output_path="build/app/outputs/flutter-apk/app-debug.apk"
                    ;;
                "release")
                    echo "   🤖 Building Android APK (release)..." >&2
                    build_output=$(flutter build apk --release 2>&1)
                    output_path="build/app/outputs/flutter-apk/app-release.apk"
                    ;;
                "bundle")
                    echo "   🤖 Building Android App Bundle..." >&2
                    build_output=$(flutter build appbundle --release 2>&1)
                    output_path="build/app/outputs/bundle/release/app-release.aab"
                    ;;
            esac
            ;;
        "ios")
            case "$build_type" in
                "debug")
                    echo "   🍎 Building iOS (debug)..." >&2
                    build_output=$(flutter build xcarchive --debug --no-codesign 2>&1)
                    output_path="build/ios/archive/Runner.xcarchive"
                    ;;
                "release")
                    echo "   🍎 Building iOS (release)..." >&2
                    build_output=$(flutter build xcarchive --release --no-codesign 2>&1)
                    output_path="build/ios/archive/Runner.xcarchive"
                    ;;
            esac
            ;;
    esac
    
    local end_time=$(date +%s)
    local build_time=$((end_time - start_time))
    
    # Check if build was successful
    if [ $? -eq 0 ]; then
        build_success=true
        local formatted_time=$(format_time "$build_time")
        
        # Get output size
        if [ -n "$output_path" ]; then
            if [[ "$platform" == "ios" ]]; then
                # For iOS, measure the app bundle directory size
                if [ -d "$output_path" ]; then
                    if [[ "$OSTYPE" == "darwin"* ]]; then
                        # macOS: du -sk returns KB, multiply by 1024 for bytes
                        output_size=$(du -sk "$output_path" 2>/dev/null | awk '{print $1 * 1024}' || echo "0")
                    else
                        # Linux: du -sb returns bytes
                        output_size=$(du -sb "$output_path" 2>/dev/null | cut -f1 || echo "0")
                    fi
                fi
            else
                # For Android, measure the file size
                output_size=$(get_file_size "$output_path")
            fi
        fi
        
        local formatted_size=$(format_size "$output_size")
        echo -e "   ✅ Build completed in: ${GREEN}${formatted_time}${NC}, Size: ${GREEN}${formatted_size}${NC}" >&2
    else
        echo -e "   ❌ Build failed after: ${RED}$(format_time "$build_time")${NC}" >&2
        echo "   Error details:" >&2
        echo "$build_output" | tail -5 | sed 's/^/     /' >&2
    fi
    
    cd "$PROJECT_ROOT"
    
    # Return build time, success status, and size
    echo "$build_time:$build_success:$output_size"
}

# Function to prepare app for builds (clean once, get dependencies once)
prepare_app_for_builds() {
    local app_dir="$1"
    local app_name="$2"
    
    echo -e "${CYAN}🔧 Preparing ${app_name} for builds...${NC}" >&2
    
    if [ ! -d "$app_dir" ]; then
        echo -e "${RED}❌ App directory not found: ${app_dir}${NC}" >&2
        return 1
    fi
    
    cd "$app_dir"
    
    echo "   🧹 Cleaning previous build artifacts..." >&2
    flutter clean > /dev/null 2>&1
    
    echo "   📦 Getting dependencies..." >&2
    flutter pub get > /dev/null 2>&1
    
    echo -e "   ✅ ${app_name} prepared for builds${NC}" >&2
    
    cd "$PROJECT_ROOT"
}

# Function to measure app builds
measure_app_builds() {
    local app_dir="$1"
    local app_name="$2"
    
    echo -e "${BLUE}📱 Measuring ${app_name} builds...${NC}" >&2
    echo "==================================" >&2
    echo "Android Builds:" >&2
    
    # Android Release (always included)
    android_release_result=$(build_and_measure "$app_dir" "android" "release" "$app_name")
    android_release_time=$(echo "$android_release_result" | cut -d':' -f1)
    android_release_success=$(echo "$android_release_result" | cut -d':' -f2)
    android_release_size=$(echo "$android_release_result" | cut -d':' -f3)
    
    # Android Debug (only if flag is set)
    if [ "$INCLUDE_DEBUG" = true ]; then
        android_debug_result=$(build_and_measure "$app_dir" "android" "debug" "$app_name")
        android_debug_time=$(echo "$android_debug_result" | cut -d':' -f1)
        android_debug_success=$(echo "$android_debug_result" | cut -d':' -f2)
        android_debug_size=$(echo "$android_debug_result" | cut -d':' -f3)
    else
        android_debug_time=0
        android_debug_success=false
        android_debug_size=0
    fi
    
    echo "" >&2
    echo "iOS Builds:" >&2
    
    # Check if iOS build is possible (macOS only)
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # iOS Release (always included)
        ios_release_result=$(build_and_measure "$app_dir" "ios" "release" "$app_name")
        ios_release_time=$(echo "$ios_release_result" | cut -d':' -f1)
        ios_release_success=$(echo "$ios_release_result" | cut -d':' -f2)
        ios_release_size=$(echo "$ios_release_result" | cut -d':' -f3)
        
        # iOS Debug (only if flag is set)
        if [ "$INCLUDE_DEBUG" = true ]; then
            ios_debug_result=$(build_and_measure "$app_dir" "ios" "debug" "$app_name")
            ios_debug_time=$(echo "$ios_debug_result" | cut -d':' -f1)
            ios_debug_success=$(echo "$ios_debug_result" | cut -d':' -f2)
            ios_debug_size=$(echo "$ios_debug_result" | cut -d':' -f3)
        else
            ios_debug_time=0
            ios_debug_success=false
            ios_debug_size=0
        fi
    else
        echo -e "${YELLOW}⚠️  iOS builds skipped (macOS required)${NC}" >&2
        ios_debug_time=0
        ios_debug_success=false
        ios_debug_size=0
        ios_release_time=0
        ios_release_success=false
        ios_release_size=0
    fi
    
    # Display results
    echo "" >&2
    echo -e "${BLUE}📊 ${app_name} Build Results:${NC}" >&2
    echo "==================================" >&2
    
    if [ "$INCLUDE_DEBUG" = true ]; then
        if [ "$android_debug_success" = true ]; then
            echo "Android Debug APK:     $(format_time "$android_debug_time") ($(format_size "$android_debug_size"))" >&2
        else
            echo "Android Debug APK:     Failed" >&2
        fi
    fi
    
    if [ "$android_release_success" = true ]; then
        echo "Android Release APK:   $(format_time "$android_release_time") ($(format_size "$android_release_size"))" >&2
    else
        echo "Android Release APK:   Failed" >&2
    fi
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
        if [ "$INCLUDE_DEBUG" = true ]; then
            if [ "$ios_debug_success" = true ]; then
                echo "iOS Debug:             $(format_time "$ios_debug_time") ($(format_size "$ios_debug_size"))" >&2
            else
                echo "iOS Debug:             Failed" >&2
            fi
        fi
        
        if [ "$ios_release_success" = true ]; then
            echo "iOS Release:           $(format_time "$ios_release_time") ($(format_size "$ios_release_size"))" >&2
        else
            echo "iOS Release:           Failed" >&2
        fi
    fi
    
    echo "" >&2
    
    # Return JSON data for this app
    cat << EOF
{
  "android": {
    "debug": {
      "build_time_seconds": $android_debug_time,
      "build_successful": $android_debug_success,
      "output_size_bytes": ${android_debug_size:-0}
    },
    "release": {
      "build_time_seconds": $android_release_time,
      "build_successful": $android_release_success,
      "output_size_bytes": ${android_release_size:-0}
    }
  },
  "ios": {
    "debug": {
      "build_time_seconds": $ios_debug_time,
      "build_successful": $ios_debug_success,
      "output_size_bytes": ${ios_debug_size:-0}
    },
    "release": {
      "build_time_seconds": $ios_release_time,
      "build_successful": $ios_release_success,
      "output_size_bytes": ${ios_release_size:-0}
    }
  }
}
EOF
}

# Main execution
echo "Starting build analysis..."
echo -e "${YELLOW}⚠️  This will take several minutes as it performs clean builds${NC}"
echo ""

# Check Flutter environment
echo -e "${CYAN}🔍 Checking Flutter environment...${NC}"
if ! command -v flutter &> /dev/null; then
    echo -e "${RED}❌ Flutter not found. Please install Flutter and add it to PATH.${NC}"
    exit 1
fi

flutter --version | head -1

echo ""
echo -e "${BLUE}📋 Preparing apps for build analysis...${NC}"
echo "============================================================="

# Prepare apps for builds (clean and get dependencies once)
prepare_app_for_builds "$PRIVATE_APP_DIR" "Private App"
prepare_app_for_builds "$PUBLIC_APP_DIR" "Public App"

echo ""
echo -e "${BLUE}🚀 Starting build measurements...${NC}"
echo "============================================================="

# Measure builds and collect data
private_app_data=$(measure_app_builds "$PRIVATE_APP_DIR" "Private App")
public_app_data=$(measure_app_builds "$PUBLIC_APP_DIR" "Public App")


if [ "$FORMAT" = "json" ]; then
    # Output JSON only
    exec 1>&3 3>&-
    cat << EOF
{
    "private_app": $private_app_data,
    "public_app": $public_app_data
}
EOF
fi
