#!/bin/bash

# Main Metrics Collection Orchestrator Script
# Runs all collectors and generates a comprehensive JSON report

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Parse command line arguments
INCLUDE_DEBUG=false
while [[ $# -gt 0 ]]; do
    case $1 in
        --debug)
            INCLUDE_DEBUG=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            echo "Usage: $0 [--debug]"
            exit 1
            ;;
    esac
done

# Get the project root and script directory
PROJECT_ROOT="$(pwd)"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
COLLECTORS_DIR="$SCRIPT_DIR/collectors"
REPORTS_DIR="$PROJECT_ROOT/metrics/reports"

# Generate timestamp for the report
TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
REPORT_FILENAME="dev-report-$(date -u +"%Y%m%d-%H%M%S").json"
REPORT_PATH="$REPORTS_DIR/$REPORT_FILENAME"

echo -e "${PURPLE}🚀 Flutter Project Optimization Metrics Collection${NC}"
echo "============================================================="
echo -e "${CYAN}Project:${NC} $(basename "$PROJECT_ROOT")"
echo -e "${CYAN}Timestamp:${NC} $TIMESTAMP"
echo -e "${CYAN}Report will be saved to:${NC} $REPORT_FILENAME"
if [ "$INCLUDE_DEBUG" = true ]; then
    echo -e "${YELLOW}📋 Debug builds enabled${NC}"
else
    echo -e "${YELLOW}📋 Release builds only (use --debug flag to include debug builds)${NC}"
fi
echo ""

# Ensure reports directory exists
mkdir -p "$REPORTS_DIR"


# Function to run a collector with json output and capture its output
run_collector_json() {
    local collector_name="$1"
    local collector_script="$2"
    local temp_file="$3"
    local extra_args="$4"
    echo -e "${BLUE}📊 Running ${collector_name}...${NC}"
    echo "============================================================="
    if [ ! -f "$collector_script" ]; then
        echo -e "${RED}❌ Collector script not found: $collector_script${NC}"
        echo "null" > "$temp_file"
        return 1
    fi
    if "$collector_script" $extra_args json > "$temp_file" 2> "$temp_file.err"; then
        echo -e "${GREEN}✅ ${collector_name} completed successfully${NC}"
    else
        echo -e "${RED}❌ ${collector_name} failed${NC}"
        echo "null" > "$temp_file"
        return 1
    fi
    echo ""
}


echo -e "${YELLOW}⚠️  This process will take several minutes due to build operations${NC}"
echo -e "${YELLOW}📋 Running all collectors in sequence...${NC}"
echo ""

# Create temporary files for collector outputs
TEMP_DIR=$(mktemp -d)
FILE_COUNT_TEMP="$TEMP_DIR/file_count"
LOC_TEMP="$TEMP_DIR/loc"
ASSETS_TEMP="$TEMP_DIR/assets"

# Dart Analysis
DART_ANALYSIS_TEMP="$TEMP_DIR/dart_analysis"
BUILD_ANALYSIS_TEMP="$TEMP_DIR/build_analysis"

# Run all collectors
echo -e "${CYAN}🔄 Starting metrics collection process...${NC}"
echo ""



# 1. File Count Analysis (fast)
run_collector_json "File Count Analysis" "$COLLECTORS_DIR/file_count.sh" "$FILE_COUNT_TEMP"

# 2. Lines of Code Analysis (fast)
run_collector_json "Lines of Code Analysis" "$COLLECTORS_DIR/lines_of_code.sh" "$LOC_TEMP"

# 3. Assets Analysis (fast)
run_collector_json "Assets Analysis" "$COLLECTORS_DIR/assets_analysis.sh" "$ASSETS_TEMP"

# 4. Dart Analysis (fast)
run_collector_json "Dart Analysis" "$COLLECTORS_DIR/dart_analysis.sh" "$DART_ANALYSIS_TEMP"

# 5. Build Analysis - Performance & Size (slow - builds everything)
if [ "$INCLUDE_DEBUG" = true ]; then
    run_collector_json "Build Analysis (with debug builds)" "$COLLECTORS_DIR/build_analysis.sh" "$BUILD_ANALYSIS_TEMP" "--debug"
else
    run_collector_json "Build Analysis (release builds only)" "$COLLECTORS_DIR/build_analysis.sh" "$BUILD_ANALYSIS_TEMP"
fi

echo -e "${CYAN}📝 Generating JSON report...${NC}"



# Extract data from collector outputs (now JSON)
FILE_COUNT_DATA=$(cat "$FILE_COUNT_TEMP")
LOC_DATA=$(cat "$LOC_TEMP")
ASSETS_DATA=$(cat "$ASSETS_TEMP")
BUILD_ANALYSIS_DATA=$(cat "$BUILD_ANALYSIS_TEMP")
DART_ANALYSIS_DATA=$(cat "$DART_ANALYSIS_TEMP")


# Generate the final JSON report
cat > "$REPORT_PATH" << EOF
{
    "metadata": {
        "timestamp": "$TIMESTAMP",
        "project_name": "$(basename "$PROJECT_ROOT")",
        "report_version": "1.0.0",
        "collection_duration": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
    },
    "file_count": $FILE_COUNT_DATA,
    "lines_of_code": $LOC_DATA,
    "assets": $ASSETS_DATA,
    "dart_analysis": $DART_ANALYSIS_DATA,
    "builds": $BUILD_ANALYSIS_DATA,
    "collector_outputs": {
        "file_count_available": "$(test -f "$FILE_COUNT_TEMP.output" && echo "true" || echo "false")",
        "loc_available": "$(test -f "$LOC_TEMP.output" && echo "true" || echo "false")",
        "assets_available": "$(test -f "$ASSETS_TEMP.output" && echo "true" || echo "false")",
        "dart_analysis_available": "$(test -f "$DART_ANALYSIS_TEMP.output" && echo "true" || echo "false")",
        "build_analysis_available": "$(test -f "$BUILD_ANALYSIS_TEMP.output" && echo "true" || echo "false")"
    }
}
EOF

echo ""
echo -e "${GREEN}🎉 Metrics collection completed successfully!${NC}"
echo "============================================================="
echo -e "${CYAN}📊 Report Summary:${NC}"
echo -e "   Report saved to: ${GREEN}$REPORT_FILENAME${NC}"
echo -e "   Full path: ${YELLOW}$REPORT_PATH${NC}"
echo -e "   Timestamp: ${YELLOW}$TIMESTAMP${NC}"
echo ""

# Show a quick summary of the collected metrics
if command -v jq &> /dev/null; then
    echo -e "${CYAN}📋 Quick Summary:${NC}"
    echo "============================================================="
    jq -r '
    "File Count - Total: \(.file_count.total), Private: \(.file_count.private), Public: \(.file_count.public)",
    "Lines of Code - Total: \(.lines_of_code.total), Private: \(.lines_of_code.private), Public: \(.lines_of_code.public)",
    "Assets: Private and Public asset analysis completed",
    "Build Performance: \(.build_performance.note // "Analysis completed")",
    "Build Sizes: \(.build_sizes.note // "Analysis completed")"
    ' "$REPORT_PATH"
else
    echo -e "${YELLOW}💡 Install 'jq' to see a formatted summary of the metrics${NC}"
    echo -e "${YELLOW}   You can view the full report at: $REPORT_PATH${NC}"
fi

echo ""
echo -e "${GREEN}✅ All metrics collected and saved to JSON report${NC}"
echo -e "${CYAN}🔗 View the report: cat $REPORT_PATH${NC}"

# Cleanup temporary files
rm -rf "$TEMP_DIR"
