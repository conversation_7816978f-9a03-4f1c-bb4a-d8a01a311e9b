/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
  line-height: 1.6;
}

/* Dashboard Container */
.dashboard-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.dashboard-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.dashboard-header h1 {
  font-size: 1.8rem;
  color: #2c3e50;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dashboard-header h1 i {
  color: #667eea;
}

.controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.controls label {
  font-weight: 500;
  color: #555;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

#reportSelect {
  padding: 0.5rem 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 0.9rem;
  background: white;
  color: #333;
  min-width: 200px;
  transition: all 0.3s ease;
}

#reportSelect:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.refresh-btn {
  padding: 0.5rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-btn:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

/* Main Content */
.dashboard-main {
  flex: 1;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
}

/* Project Info Section */
.project-info-section {
  margin-bottom: 2rem;
}

.project-info-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-left: 4px solid #667eea;
}

.project-info-card h3 {
  margin-bottom: 1rem;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.project-info-card h3 i {
  color: #667eea;
}

/* Overview Section */
.overview-section {
  margin-bottom: 2rem;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.overview-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.overview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.overview-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.overview-card-title {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.overview-card-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
}

.overview-card-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.overview-card-value {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
}

.overview-card-trend {
  margin-left: 1rem;
}

.overview-card-subtitle {
  font-size: 0.85rem;
  color: #888;
}

/* Trend Indicators */
.trend-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.trend-indicator.positive {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.trend-indicator.negative {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.trend-indicator.neutral {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  border: 1px solid rgba(107, 114, 128, 0.2);
}

.trend-indicator i {
  font-size: 0.7rem;
}

/* Comparison Badges */
.comparison-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border: 1px solid rgba(16, 185, 129, 0.2);
  margin-left: 0.5rem;
}

.no-comparison-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  border: 1px solid rgba(107, 114, 128, 0.2);
}

/* Build Time Trends */
.build-time-trend {
  margin-top: 0.25rem;
}

.build-time-trend .trend-indicator {
  font-size: 0.65rem;
  padding: 0.15rem 0.35rem;
}

/* Assets Overview Card */
.assets-card .overview-card-value {
  font-size: 1.2rem;
}

.assets-overview {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.asset-count-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background: rgba(245, 158, 11, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(245, 158, 11, 0.1);
}

.asset-label {
  font-size: 0.85rem;
  font-weight: 600;
  color: #92400e;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.asset-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1f2937;
}

.assets-trends {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-top: 0.5rem;
}

.asset-trend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
}

.trend-label {
  font-weight: 600;
  color: #6b7280;
  min-width: 45px;
}

.asset-trend-item .trend-indicator {
  font-size: 0.65rem;
  padding: 0.15rem 0.35rem;
}

/* Charts Section */
.charts-section {
  margin-bottom: 2rem;
}

.chart-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
}

.chart-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.chart-card h3 {
  margin-bottom: 1rem;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chart-card h3 i {
  color: #667eea;
}

.chart-card canvas {
  max-height: 300px;
}

/* Code Quality Card in Charts Section */
.chart-card.code-quality-card {
  display: flex;
  flex-direction: column;
}

.chart-card.code-quality-card #codeQuality {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Details Section */
.details-section {
  margin-bottom: 2rem;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* Asset Analysis Card - Make it wider */
.detail-card.asset-analysis-wide {
  grid-column: 1 / -1; /* Span full width */
  max-width: none;
}

.detail-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.detail-card h3 {
  margin-bottom: 1rem;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #f1f5f9;
}

.detail-card h3 i {
  color: #667eea;
}

/* Tables */
.metrics-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.metrics-table th {
  background: #f8fafc;
  color: #374151;
  font-weight: 600;
  padding: 0.75rem;
  text-align: left;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metrics-table td {
  padding: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
  color: #374151;
}

.metrics-table tr:hover {
  background: #f9fafb;
}

.metrics-table tr:last-child td {
  border-bottom: none;
}

/* Progress Bars */
.progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  margin: 0.5rem 0;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-success {
  background: #dcfce7;
  color: #166534;
}

.status-warning {
  background: #fef3c7;
  color: #92400e;
}

.status-error {
  background: #fee2e2;
  color: #991b1b;
}

.status-info {
  background: #dbeafe;
  color: #1e40af;
}

.status-not-built {
  background: #f3f4f6;
  color: #6b7280;
}

/* Build Type Chips */
.build-chip {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.build-chip.release {
  background: #dcfce7;
  color: #166534;
}

.build-chip.debug {
  background: #e0e7ff;
  color: #3730a3;
}

/* Asset Analysis Grid */
.asset-analysis-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  overflow: hidden;
}

.asset-table-container {
  min-width: 0; /* Allow shrinking */
  overflow: hidden;
}

.asset-table-container h4 {
  margin-bottom: 1rem;
  color: #374151;
  font-size: 1.05rem;
  font-weight: 600;
}

.asset-table-container .metrics-table {
  margin-top: 0;
  font-size: 0.9rem;
  width: 100%;
  table-layout: fixed; /* Fixed table layout for better control */
}

.asset-table-container .metrics-table th,
.asset-table-container .metrics-table td {
  padding: 0.75rem 0.6rem;
  word-wrap: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Specific column widths for asset tables */
.asset-table-container:first-child .metrics-table th:first-child,
.asset-table-container:first-child .metrics-table td:first-child {
  width: 50%; /* Asset name column */
}

.asset-table-container:first-child .metrics-table th:nth-child(2),
.asset-table-container:first-child .metrics-table td:nth-child(2) {
  width: 20%; /* Source column */
  text-align: center;
}

.asset-table-container:first-child .metrics-table th:last-child,
.asset-table-container:first-child .metrics-table td:last-child {
  width: 30%; /* Size column */
  text-align: right;
}

.asset-table-container:last-child .metrics-table th:first-child,
.asset-table-container:last-child .metrics-table td:first-child {
  width: 25%; /* Type column */
}

.asset-table-container:last-child .metrics-table th:nth-child(2),
.asset-table-container:last-child .metrics-table td:nth-child(2) {
  width: 20%; /* Count column */
  text-align: center;
}

.asset-table-container:last-child .metrics-table th:last-child,
.asset-table-container:last-child .metrics-table td:last-child {
  width: 55%; /* Total size column */
  text-align: right;
}

/* Source badges */
.source-badge {
  display: inline-block;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.source-badge.private {
  background: #e0e7ff;
  color: #3730a3;
}

.source-badge.public {
  background: #dcfce7;
  color: #166534;
}

/* Asset Size Formatting */
.asset-size {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.85rem;
  color: #6b7280;
}

.asset-size.large {
  color: #dc2626;
  font-weight: 600;
}

.asset-size.medium {
  color: #d97706;
  font-weight: 500;
}

.asset-size.small {
  color: #059669;
}

/* Build Results Specific Styling */
.build-results-card {
  grid-column: 1 / -1; /* Take full width */
}

.build-results-container {
  overflow-x: auto;
  margin: -0.5rem;
  padding: 0.5rem;
}

.build-results-container .metrics-table {
  min-width: 600px;
  white-space: nowrap;
}

.build-results-container .metrics-table td {
  padding: 0.5rem 0.75rem;
  font-size: 0.9rem;
}

.build-results-container .metrics-table th {
  padding: 0.5rem 0.75rem;
  font-size: 0.8rem;
}

/* Build Results Organized Layout */
.build-results-organized {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-top: 1rem;
}

.build-group {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.build-group-header {
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #f1f5f9;
}

.build-group-header h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.build-group-header h4 i {
  font-size: 1rem;
}

.build-group:has(.build-card.success) .build-group-header h4 i {
  color: #10b981;
}

.build-group:has(.build-card.failed) .build-group-header h4 i {
  color: #ef4444;
}

.build-group:has(.build-card.not-built) .build-group-header h4 i {
  color: #6b7280;
}

.build-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1rem;
}

/* Build Card Styling */
.build-card {
  background: #f8fafc;
  border-radius: 10px;
  padding: 1.25rem;
  border: 2px solid transparent;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.build-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: #e5e7eb;
}

.build-card.success::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.build-card.failed::before {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.build-card.not-built::before {
  background: linear-gradient(90deg, #6b7280, #4b5563);
}

.build-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.build-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.build-card-title {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.app-name {
  font-weight: 700;
  font-size: 1.1rem;
  color: #1f2937;
}

.platform-name {
  font-weight: 500;
  font-size: 0.9rem;
  color: #6b7280;
  text-transform: capitalize;
}

.build-type-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.build-type-badge.debug {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.build-type-badge.release {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.build-card-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.build-metric {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.metric-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  font-size: 0.9rem;
}

.metric-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.metric-label {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.build-card-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.85rem;
}

.build-card.success .build-card-status {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
}

.build-card.failed .build-card-status {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

.build-card.not-built .build-card-status {
  background: rgba(107, 114, 128, 0.1);
  color: #4b5563;
}

/* Code Quality Metrics */
.quality-metric {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  margin: 0.5rem 0;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #e5e7eb;
}

.quality-metric.errors {
  border-left-color: #dc2626;
}

.quality-metric.warnings {
  border-left-color: #d97706;
}

.quality-metric.info {
  border-left-color: #2563eb;
}

.quality-metric-label {
  font-weight: 500;
  color: #374151;
}

.quality-metric-value {
  font-size: 1.25rem;
  font-weight: 700;
}

.quality-metric.errors .quality-metric-value {
  color: #dc2626;
}

.quality-metric.warnings .quality-metric-value {
  color: #d97706;
}

.quality-metric.info .quality-metric-value {
  color: #2563eb;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.loading-spinner {
  text-align: center;
  color: #667eea;
}

.loading-spinner i {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.loading-spinner p {
  font-size: 1.1rem;
  font-weight: 500;
}

.loading-overlay.hidden {
  display: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .asset-analysis-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .dashboard-main {
    padding: 1rem;
  }

  .header-content {
    padding: 0 1rem;
    flex-direction: column;
    align-items: stretch;
  }

  .overview-cards {
    grid-template-columns: 1fr;
  }

  .chart-grid {
    grid-template-columns: 1fr;
  }

  .details-grid {
    grid-template-columns: 1fr;
  }

  .chart-card canvas {
    max-height: 250px;
  }

  .build-cards-grid {
    grid-template-columns: 1fr;
  }

  .build-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .build-metric {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .metric-info {
    align-items: center;
  }

  .asset-analysis-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .asset-table-container .metrics-table {
    font-size: 0.9rem;
  }

  .asset-table-container .metrics-table th,
  .asset-table-container .metrics-table td {
    padding: 0.6rem 0.5rem;
  }
}

@media (max-width: 480px) {
  .dashboard-header h1 {
    font-size: 1.4rem;
  }

  .overview-card-value {
    font-size: 1.5rem;
  }

  .controls {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  #reportSelect {
    min-width: auto;
  }

  .build-cards-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .build-group {
    padding: 1rem;
  }

  .build-card {
    padding: 1rem;
  }

  .build-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .app-name {
    font-size: 1rem;
  }

  .build-metric {
    flex-direction: row;
    gap: 0.5rem;
  }

  .metric-info {
    align-items: flex-start;
  }

  .assets-overview {
    gap: 0.375rem;
  }

  .asset-count-item {
    padding: 0.375rem 0.5rem;
  }

  .asset-label {
    font-size: 0.8rem;
  }

  .asset-value {
    font-size: 1rem;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
