# Dashboard Comparison Feature

## Overview
The dashboard now includes a comparison feature that automatically compares the selected report with the immediate previously generated report based on the timestamp in the filename.

## Features Implemented

### 1. Automatic Previous Report Detection
- The system automatically identifies the previous report based on the filename timestamp
- Reports are sorted by date/time (newest first)
- The previous report is the next older report in the chronological sequence

### 2. Comparison Indicators
The dashboard shows trend indicators with percentage changes for:

#### Overview Cards
- **Total Files**: Shows increase/decrease in project file count
- **Lines of Code**: Shows increase/decrease in total LOC
- **Assets Overview**: Shows public and private asset counts with separate trend indicators for each

#### Build Results
- **Organized by Status**: Builds are grouped into "Successful Builds", "Failed Builds", and "Not Built"
- **Build Times**: Individual build time trends for each app/platform/type combination
- **Improved Layout**: Clean card-based design with better visual hierarchy
- Shows percentage increase/decrease in build times

### 3. Visual Indicators
- **Green arrows (↑)**: Positive changes (increases)
- **Red arrows (↓)**: Negative changes (decreases)  
- **Gray dash (-)**: No significant change (< 0.1%)

### 4. Project Information
- Shows comparison status in the project info section
- Displays the timestamp of the previous report being compared against
- Shows "No previous report available" when no comparison is possible

## Technical Implementation

### Key Components Added

1. **Properties**:
   - `previousReport`: Stores the previous report data
   - `availableReports`: Array of all available report filenames

2. **Methods**:
   - `loadPreviousReport()`: Loads the previous report for comparison
   - `calculatePercentageChange()`: Calculates percentage change between values
   - `formatTrendIndicator()`: Formats trend indicators with icons and percentages
   - `getComparisonValue()`: Helper to extract comparison values from reports
   - `getBuildCompletionComparison()`: Compares build completion counts
   - `getBuildTimeTrend()`: Gets build time trend for specific builds

3. **CSS Classes**:
   - `.trend-indicator`: Base styling for trend indicators
   - `.trend-indicator.positive`: Green styling for increases
   - `.trend-indicator.negative`: Red styling for decreases
   - `.trend-indicator.neutral`: Gray styling for no change
   - `.comparison-badge`: Green badge for active comparison
   - `.no-comparison-badge`: Gray badge when no comparison available
   - `.build-results-organized`: New organized layout for build results
   - `.build-group`: Styling for build status groups
   - `.build-card`: Individual build card styling
   - `.assets-card`: Special styling for assets overview card
   - `.assets-overview`: Layout for public/private asset counts
   - `.asset-count-item`: Individual asset count display styling

### File Changes
- `app.js`: Added comparison logic, trend calculation, and reorganized build results
- `style.css`: Added styling for trend indicators, comparison badges, and new build card layout
- `index.html`: Updated card structure to accommodate trend indicators

## Usage

### Automatic Operation
- The comparison feature works automatically when viewing any report
- No user interaction required
- Comparison data appears immediately when a previous report is available

### Comparison Logic
- Compares with the immediate previous report by timestamp
- If no previous report exists, no comparison indicators are shown
- Handles missing or invalid data gracefully

### Example Scenarios

1. **First Report**: No comparison available, no trend indicators shown
2. **Second Report**: Compares with first report, shows trends
3. **Subsequent Reports**: Always compares with the immediate previous report

## Benefits

1. **Quick Insights**: Instantly see if metrics are improving or degrading
2. **Build Performance Tracking**: Monitor build time trends across releases
3. **Code Growth Monitoring**: Track file count and LOC changes over time
4. **Visual Clarity**: Color-coded indicators make trends immediately apparent

## Future Enhancements

Potential improvements that could be added:
- Compare with any selected previous report (not just immediate previous)
- Historical trend charts showing multiple data points
- Configurable comparison thresholds
- Export comparison reports
- Alert notifications for significant changes
