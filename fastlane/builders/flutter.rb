require_relative '../configs/base'
require_relative '../lib/utils'
require_relative 'android'
require_relative 'ios'

module FlutterBuilder
    include FastlaneConfig

    def self.prebuild_setup
        system('flutter clean')
        system('flutter pub get')
        system('flutter pub run build_runner build --delete-conflicting-outputs')
        system('flutter pub run easy_localization:generate --source-dir assets/translations --output-dir lib/translations')
        system('flutter pub run easy_localization:generate --source-dir assets/translations --output-dir lib/translations --output-file locale_keys.g.dart --format keys')

        Context.UI.success "Prebuild setup completed"
    end

    def self.code_generation(app)
        Context.UI.message "Running code generation for #{app} app"

        Dir.chdir(Context.get_app_directory) do
            prebuild_setup
        end

        Context.UI.success "Code generation completed"
    end

    def self.build(options)
        Context.UI.message "Initialising build process..."

        # Get platform from options
        platform = UserInput.get_platform(options)

        # Get android artifacts if building for android
        if %w(both android).include?(platform)
            UserInput.get_android_artifacts(options)
        end

        # Sets environment, version and iteration to lane context
        Context.set_env(options)
        Context.set_version
        Context.set_iteration

        # Ensure output directory exists
        Utils.ensure_output_directory

        Dir.chdir(Context.get_app_directory) do
            # prebuild_setup
        end
        
        begin
            case platform
            when "both"
                Context.UI.message "Building for Android 🤖 & iOS 🍎"
                AndroidBuilder.build_artifacts
                IOSBuilder.build_xcarchive
            when "android"
                Context.UI.message "Building for Android 🤖"
                AndroidBuilder.build_artifacts
            when "ios"
                Context.UI.message "Building for iOS 🍎"
                IOSBuilder.build_xcarchive
            else
                FastlaneCore::UI.error "Unimplemented platform: #{platform}"
            end

            Context.UI.success "Build complete"
        rescue => e
            Context.UI.error "Build Failed: #{e.message}"
            raise e
        end

        Context.save_iteration
        # if %w(both android).include?(platform)
        # end
    end
end