module Android<PERSON><PERSON><PERSON>
    def self.app_config
        app = Context.lane_context[Context.sv::FL_APP]
        FastlaneConfig.get_app_config(app)
    end

    def self.build_artifacts
        artifacts = Context.lane_context[Context.sv::FL_ARTIFACTS]
        raise "[artifacts] not set" unless artifacts

        Dir.chdir("../#{app_config[:app_directory]}") do
            case artifacts
            when "split_apk"
                build_split_apk
            when "fat_apk"
                build_apk
            when "aab"
                build_aab
            else
                Context.UI.user_error! "Invalid artifacts selection: #{artifacts}"
            end
        end
    end

    def self.build_split_apk
        prefix = app_config[:app_prefix]
        Context.UI.message "Building split APK for #{prefix}"
        system("flutter build apk --release --split-per-abi --dart-define-from-file=#{Context.get_env_path}")

        file_name = Utils.get_release_file_name("#{prefix}_arm64_v8", "apk")

        source_path = "#{Context.get_app_directory}/#{FastlaneConfig::APK_PATH}/#{FastlaneConfig::ARM64_V8_FILE_NAME}"
        target_path = "#{Context.get_output_directory}/#{file_name}"

        Utils.copy_file(source_path, target_path)
    end

    def self.build_apk
        prefix = app_config[:app_prefix]
        Context.UI.message "Building fat APK for #{prefix}"
        system("flutter build apk --release --dart-define-from-file=#{Context.get_env_path}")

        file_name = Utils.get_release_file_name(prefix, "apk")

        source_path = "#{Context.get_app_directory}/#{FastlaneConfig::APK_PATH}/#{FastlaneConfig::APK_FILE_NAME}"
        target_path = "#{Context.get_output_directory}/#{file_name}"

        Utils.copy_file(source_path, target_path)
    end

    def self.build_aab
        prefix = app_config[:app_prefix]
        Context.UI.message "Building AAB for #{prefix}"
        system("flutter build appbundle --release --dart-define-from-file=#{Context.get_env_path}")

        file_name = Utils.get_release_file_name(prefix, "aab")

        source_path = "#{Context.get_app_directory}/#{FastlaneConfig::AAB_PATH}/#{FastlaneConfig::AAB_FILE_NAME}"
        target_path = "#{Context.get_output_directory}/#{file_name}"

        Utils.copy_file(source_path, target_path)
    end
end