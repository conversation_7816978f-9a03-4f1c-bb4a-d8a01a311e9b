module IOSBuilder
	def self.build_xcarchive
		prefix = Context.lane_context[Context.sv::FL_APP]
		Context.UI.message "Building iOS xcarchive for #{prefix}"
		
		# Install CocoaPods dependencies
		Context.UI.message "Installing CocoaPods dependencies"
		ios_dir = "#{Context.get_app_directory}/ios"
		Dir.chdir(ios_dir) do
			system("pod repo update")
			system("pod install")
		end

		# Build xcarchive
		Dir.chdir(Context.get_app_directory) do
			system("flutter build xcarchive --release --dart-define-from-file=#{Context.get_env_path}")
		end

		file_name = Utils.get_release_file_name("Runner", "xcarchive")
		
		# Copy xcarchive to output directory
		source_path = "#{Context.get_app_directory}/#{FastlaneConfig::XC_PATH}/#{FastlaneConfig::XC_FILE_NAME}"
		target_path = "#{Context.get_output_directory}/#{file_name}"

		Utils.copy_xcarchive(source_path, target_path)

		Context.UI.success "iOS archived at #{FastlaneConfig::XC_PATH}"
	end
end