require_relative '../configs/base'

module Context
    include FastlaneConfig

    def self.sv
        FastlaneConfig::SharedVal<PERSON>
    end

    def self.UI
        FastlaneCore::UI
    end

    def self.lane_context
        Fastlane::Actions.lane_context
    end
    
    def self.app_config
        app = lane_context[sv::FL_APP]
        FastlaneConfig.get_app_config(app)
    end

    def self.get_env_path
        env_file = lane_context[sv::FL_ENV_FILE]
        secret_path = app_config[:secret_path]
        return "#{secret_path}/#{env_file}"
    end

    def self.save_project_root(pwd)
        lane_context[sv::FL_PROJECT_ROOT] = pwd
        self.UI.success "Project root set: #{pwd}"
    end

    def self.get_app_directory
        root = lane_context[sv::FL_PROJECT_ROOT]
        return "#{root}/#{app_config[:app_directory]}"
    end

    def self.get_output_directory
        project_root = lane_context[sv::FL_PROJECT_ROOT]
        return "#{project_root}/#{app_config[:output_path]}"
    end

    def self.set_env(options)
        env = UserInput.get_env(options)
        file = "#{env}.json"
        lane_context[sv::FL_ENV_FILE] = file
        self.UI.success "Env file set: #{file}"
    end

    def self.set_version
        pubspec_path = "../#{app_config[:app_directory]}/pubspec.yaml"
        version = Utils.get_pubspec_version(pubspec_path)
        lane_context[sv::FL_VERSION] = version
        self.UI.success "Version set to: #{version}"
    end

    def self.set_iteration
        current = 0
        
        if File.exist?(ITERATION_FILE)
            content = File.read(ITERATION_FILE)
            require 'yaml'
            data = YAML.safe_load(content)
            app = lane_context[sv::FL_APP]
            current = data&.fetch(app, {})&.fetch(Utils.get_date, 0) || 0
        end

        iteration = current + 1
        if iteration <= current
            self.UI.error "Sorry, this iteration (#{iteration}) is already built. Current: #{current}. Try next number."
            iteration = current + 1
        end

        lane_context[sv::FL_ITERATION] = iteration
        self.UI.success "Iteration set to: #{iteration}"
    end

    def self.save_iteration
        iteration = lane_context[sv::FL_ITERATION]
        return unless iteration
        
        # Ensure the directory exists
        dir = File.dirname(ITERATION_FILE)
        Dir.mkdir(dir) unless Dir.exist?(dir)
        
        require 'yaml'
        app = lane_context[sv::FL_APP]
        date = Utils.get_date
        data = {}
        if File.exist?(ITERATION_FILE)
            content = File.read(ITERATION_FILE)
            data = YAML.safe_load(content) || {}
        end

        data[app] ||= {}
        data[app][date] = iteration

        File.open(ITERATION_FILE, "w") { |f| f.write(data.to_yaml) }
        self.UI.success "Iteration #{iteration} saved to #{ITERATION_FILE}"
    end
end